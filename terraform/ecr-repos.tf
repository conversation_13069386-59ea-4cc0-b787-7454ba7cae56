locals {
  ecr_repositories_all = {
    dev = ["enterprise-account-api" , "enterprise-general-api",
           "enterprise-blacklist-api","enterprise-webservice-api",
           "enterprise-report-api"],
    stg = []
    prd = []
  }
  ecr_repositories = local.ecr_repositories_all[local.env]
}
module "ecr" {
  for_each        = toset(local.ecr_repositories)
  source          = "./modules/ecr-repository"
  repository_name = each.key
}